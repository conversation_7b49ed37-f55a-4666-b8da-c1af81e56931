@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-geist-sans: '<PERSON>eist Sans';
    --font-geist-mono: 'Geist Mono';
    --font-inter: 'Inter';
    --font-ibm-plex-serif: 'IBM Plex Serif';
  }

  /* Font styling for IBM Plex Serif */
  html[data-font="ibm-plex-serif"] {
    font-family: var(--font-ibm-plex-serif), Georgia, serif;
  }
}

@layer components {
  .markdown-content {
    @apply prose prose-sm prose-gray max-w-none;

    h1 {
      @apply text-2xl font-semibold mb-2;
    }
    h2 {
      @apply text-xl font-semibold mb-2;
    }
    h3 {
      @apply text-lg font-semibold mb-2;
    }
    h4 {
      @apply text-base font-semibold mb-2;
    }

    /* Base text size */
    p, ul, ol {
      @apply text-sm text-gray-700 leading-relaxed;
    }

    /* Lists */
    ul {
      @apply list-disc pl-4 mb-4 space-y-1;
    }
    ol {
      @apply list-decimal pl-4 mb-4 space-y-1;
    }

    /* Links */
    a {
      @apply text-blue-600 hover:text-blue-800 no-underline hover:underline;
    }

    /* Code blocks */
    pre {
      @apply bg-gray-50 p-4 rounded-lg mb-4 overflow-x-auto text-sm;
    }
    code {
      @apply bg-gray-50 px-1.5 py-0.5 rounded text-sm font-normal text-gray-800;
    }

    /* Blockquotes */
    blockquote {
      @apply border-l-4 border-gray-200 pl-4 italic my-4 text-gray-600;
    }

    /* Lists inside lists */
    ul ul, ol ol, ul ol, ol ul {
      @apply mt-1 mb-1;
    }

    /* Tables */
    table {
      @apply w-full border-collapse mb-4 text-sm;
    }

    thead {
      @apply bg-gray-50;
    }

    th {
      @apply border border-gray-200 px-3 py-2 text-left font-medium text-gray-700;
    }

    td {
      @apply border border-gray-200 px-3 py-2 text-gray-700;
    }

    tr:nth-child(even) {
      @apply bg-gray-50;
    }
  }
}

/* Pulse animation for search indicator */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.5;
  }
}

.pulse-dot {
  animation: pulse 2s infinite;
}
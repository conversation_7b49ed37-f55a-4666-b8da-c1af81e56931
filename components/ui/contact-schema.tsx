"use client";

import { FC } from "react";
import type { ContactPage, WithContext } from "schema-dts";
import config from "@/config";

interface ContactSchemaProps {
  companyName?: string;
  email?: string;
  phone?: string;
  address?: string;
  url?: string;
  description?: string;
}

export const ContactSchema: FC<ContactSchemaProps> = ({
  companyName = config.contact?.contactInfo?.companyName || config.title,
  email = config.contact?.contactInfo?.email || "<EMAIL>",
  phone = config.contact?.contactInfo?.phone || "******-123-4567",
  address = config.contact?.contactInfo?.address ||
    "123 Main St, Anytown, AN 12345",
  url = `${config.url}/contact` || "https://example.com/contact",
  description = config.contact?.schema?.description ||
    config.contact?.description ||
    "Get in touch with our team for any questions or support needs.",
}) => {
  // Create type-safe schema using schema-dts
  const contactSchema: WithContext<ContactPage> = {
    "@context": "https://schema.org",
    "@type": "ContactPage",
    name: `Contact ${companyName}`,
    description: description,
    mainEntity: {
      "@type": "Organization",
      name: companyName,
      email: email,
      telephone: phone,
      address: {
        "@type": "PostalAddress",
        streetAddress: address,
      },
      url: config.url,
    },
    url: url,
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(contactSchema) }}
    />
  );
};

---
title: Reference Documentation
description: Complete reference documentation for Bordful job board configurations, systems, and features.
lastUpdated: "2025-05-22"
---

# Bordful Reference Documentation

This section provides comprehensive reference documentation for all Bordful configuration options, systems, and features. Use these references as your guide to understanding and implementing all available options.

## Systems Documentation

- [**Language System**](./language-system.md) - Complete documentation on the internationalization-ready language system, including language codes and implementation.

- [**FAQ System**](./faq-system.md) - Reference for the feature-rich FAQ page with client-side search, schema markup, and more.

- [**RSS Feed System**](./rss-feed-system.md) - Guide to the comprehensive RSS feed system for job listings.

- [**Robots.txt Generation**](./robots-generation.md) - Reference for automatic robots.txt generation and crawl control.

- [**Sitemap Generation**](./sitemap-generation.md) - Documentation for automatic XML sitemap generation with ISR updates.

## Data Structures

- [**Salary Structure**](./salary-structure.md) - Complete reference for the comprehensive salary handling system with multiple currencies and formats.

- [**Pagination, Sorting, and URL Parameters**](./pagination-sorting.md) - Documentation for the pagination system, sorting options, and URL parameters.

- [**Currencies**](./currencies.md) - Complete reference for all supported currencies, including fiat, cryptocurrency, and stablecoins.

## Coming Soon

- **Configuration Options** - Comprehensive guide to all configuration options and their effects.

- **Environment Variables** - Complete reference for all environment variables used by Bordful.

- **Language Codes** - Reference for all supported language codes and their implementation.

- **Career Levels** - Documentation for standardized career levels and their implementation.

- **Glossary** - Comprehensive glossary of technical terms used throughout Bordful.

- **CLI Commands** - Reference for any command-line tools and utilities provided with Bordful.

- **File Structure** - Complete reference for the Bordful file and directory structure.

## How to Use This Section

This reference documentation is designed to be:

1. **Comprehensive** - Covering all available options and configurations
2. **Detailed** - Providing specific implementation details
3. **Practical** - Including examples for all options
4. **Up-to-date** - Maintained alongside code changes

Use the sidebar navigation to quickly find the specific reference documentation you need. Each reference document includes complete examples and code snippets to help you implement the features correctly. 
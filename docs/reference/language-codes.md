---
title: Language Codes Reference
description: Complete reference for all language codes supported in Bordful job board.
lastUpdated: "2025-05-22"
---

# Language Codes Reference

Bordful supports a wide range of languages for job listings, enabling you to create a truly international job board. This reference documents all supported language codes and their implementation.

## Language Code Format

Bordful uses ISO 639-1 two-letter language codes for all language-related functionality. These standardized codes ensure compatibility with browsers, search engines, and other systems.

## Supported Language Codes

Bordful supports the following language codes:

| Code | Language Name     | Notes            |
| ---- | ----------------- | ---------------- |
| `ab` | Abkhazian         |                  |
| `aa` | Afar              |                  |
| `af` | Afrikaans         |                  |
| `ak` | Akan              |                  |
| `sq` | Albanian          |                  |
| `am` | Amharic           |                  |
| `ar` | Arabic            |                  |
| `an` | Aragonese         |                  |
| `hy` | Armenian          |                  |
| `as` | Assamese          |                  |
| `av` | Avaric            |                  |
| `ae` | Avestan           |                  |
| `ay` | Aymara            |                  |
| `az` | Azerbaijani       |                  |
| `bm` | Bambara           |                  |
| `ba` | Bashkir           |                  |
| `eu` | Basque            |                  |
| `be` | Belarusian        |                  |
| `bn` | Bengali           |                  |
| `bi` | Bislama           |                  |
| `bs` | Bosnian           |                  |
| `br` | Breton            |                  |
| `bg` | Bulgarian         |                  |
| `my` | Burmese           |                  |
| `ca` | Catalan           |                  |
| `ch` | Chamorro          |                  |
| `ce` | Chechen           |                  |
| `zh` | Chinese           |                  |
| `cu` | Church Slavic     |                  |
| `cv` | Chuvash           |                  |
| `kw` | Cornish           |                  |
| `co` | Corsican          |                  |
| `cr` | Cree              |                  |
| `hr` | Croatian          |                  |
| `cs` | Czech             |                  |
| `da` | Danish            |                  |
| `dv` | Divehi            |                  |
| `nl` | Dutch             |                  |
| `dz` | Dzongkha          |                  |
| `en` | English           | Default language |
| `eo` | Esperanto         |                  |
| `et` | Estonian          |                  |
| `ee` | Ewe               |                  |
| `fo` | Faroese           |                  |
| `fj` | Fijian            |                  |
| `fi` | Finnish           |                  |
| `fr` | French            |                  |
| `fy` | Frisian           |                  |
| `ff` | Fulah             |                  |
| `gl` | Galician          |                  |
| `lg` | Ganda             |                  |
| `ka` | Georgian          |                  |
| `de` | German            |                  |
| `el` | Greek             |                  |
| `kl` | Greenlandic       |                  |
| `gn` | Guarani           |                  |
| `gu` | Gujarati          |                  |
| `ht` | Haitian Creole    |                  |
| `ha` | Hausa             |                  |
| `he` | Hebrew            |                  |
| `hz` | Herero            |                  |
| `hi` | Hindi             |                  |
| `ho` | Hiri Motu         |                  |
| `hu` | Hungarian         |                  |
| `is` | Icelandic         |                  |
| `io` | Ido               |                  |
| `ig` | Igbo              |                  |
| `id` | Indonesian        |                  |
| `ia` | Interlingua       |                  |
| `ie` | Interlingue       |                  |
| `iu` | Inuktitut         |                  |
| `ik` | Inupiaq           |                  |
| `ga` | Irish             |                  |
| `it` | Italian           |                  |
| `ja` | Japanese          |                  |
| `jv` | Javanese          |                  |
| `kn` | Kannada           |                  |
| `kr` | Kanuri            |                  |
| `ks` | Kashmiri          |                  |
| `kk` | Kazakh            |                  |
| `km` | Khmer             |                  |
| `ki` | Kikuyu            |                  |
| `rw` | Kinyarwanda       |                  |
| `ky` | Kyrgyz            |                  |
| `kv` | Komi              |                  |
| `kg` | Kongo             |                  |
| `ko` | Korean            |                  |
| `ku` | Kurdish           |                  |
| `kj` | Kuanyama          |                  |
| `lo` | Lao               |                  |
| `la` | Latin             |                  |
| `lv` | Latvian           |                  |
| `li` | Limburgish        |                  |
| `ln` | Lingala           |                  |
| `lt` | Lithuanian        |                  |
| `lu` | Luba-Katanga      |                  |
| `lb` | Luxembourgish     |                  |
| `mk` | Macedonian        |                  |
| `mg` | Malagasy          |                  |
| `ms` | Malay             |                  |
| `ml` | Malayalam         |                  |
| `mt` | Maltese           |                  |
| `gv` | Manx              |                  |
| `mi` | Maori             |                  |
| `mr` | Marathi           |                  |
| `mh` | Marshallese       |                  |
| `mn` | Mongolian         |                  |
| `na` | Nauru             |                  |
| `nv` | Navajo            |                  |
| `ne` | Nepali            |                  |
| `nd` | North Ndebele     |                  |
| `se` | Northern Sami     |                  |
| `no` | Norwegian         |                  |
| `nb` | Norwegian Bokmål  |                  |
| `nn` | Norwegian Nynorsk |                  |
| `ng` | Ndonga            |                  |
| `ny` | Nyanja            |                  |
| `oc` | Occitan           |                  |
| `oj` | Ojibwa            |                  |
| `or` | Oriya             |                  |
| `om` | Oromo             |                  |
| `os` | Ossetian          |                  |
| `pi` | Pali              |                  |
| `ps` | Pashto            |                  |
| `fa` | Persian           |                  |
| `pl` | Polish            |                  |
| `pt` | Portuguese        |                  |
| `pa` | Punjabi           |                  |
| `qu` | Quechua           |                  |
| `ro` | Romanian          |                  |
| `rm` | Romansh           |                  |
| `rn` | Rundi             |                  |
| `ru` | Russian           |                  |
| `sm` | Samoan            |                  |
| `sg` | Sango             |                  |
| `sa` | Sanskrit          |                  |
| `sc` | Sardinian         |                  |
| `gd` | Scottish Gaelic   |                  |
| `sr` | Serbian           |                  |
| `sn` | Shona             |                  |
| `ii` | Sichuan Yi        |                  |
| `sd` | Sindhi            |                  |
| `si` | Sinhala           |                  |
| `sk` | Slovak            |                  |
| `sl` | Slovenian         |                  |
| `so` | Somali            |                  |
| `nr` | South Ndebele     |                  |
| `st` | Southern Sotho    |                  |
| `es` | Spanish           |                  |
| `su` | Sundanese         |                  |
| `sw` | Swahili           |                  |
| `ss` | Swati             |                  |
| `sv` | Swedish           |                  |
| `tl` | Tagalog           |                  |
| `ty` | Tahitian          |                  |
| `tg` | Tajik             |                  |
| `ta` | Tamil             |                  |
| `tt` | Tatar             |                  |
| `te` | Telugu            |                  |
| `th` | Thai              |                  |
| `bo` | Tibetan           |                  |
| `ti` | Tigrinya          |                  |
| `to` | Tongan            |                  |
| `ts` | Tsonga            |                  |
| `tn` | Tswana            |                  |
| `tr` | Turkish           |                  |
| `tk` | Turkmen           |                  |
| `tw` | Twi               |                  |
| `ug` | Uyghur            |                  |
| `uk` | Ukrainian         |                  |
| `ur` | Urdu              |                  |
| `uz` | Uzbek             |                  |
| `ve` | Venda             |                  |
| `vi` | Vietnamese        |                  |
| `vo` | Volapük           |                  |
| `wa` | Walloon           |                  |
| `cy` | Welsh             |                  |
| `wo` | Wolof             |                  |
| `xh` | Xhosa             |                  |
| `yi` | Yiddish           |                  |
| `yo` | Yoruba            |                  |
| `za` | Zhuang            |                  |
| `zu` | Zulu              |                  |

## Using Language Codes in Bordful

### Job Listings

Job listings can be associated with multiple languages to indicate what languages are required or preferred for the position. In Airtable, this is implemented through the `languages` field.

Bordful supports three formats for storing language data in Airtable:

1. **ISO codes directly**: `en`, `fr`
2. **Language Name (code) format**: `English (en)`, `French (fr)`
3. **Language names**: `English`, `French` (via lookup)

The system will automatically normalize these formats to standard ISO 639-1 codes.

### Language Filtering

Users can filter job listings by language requirements. The language filter is available in the job listing sidebar and creates URLs like:

```
/jobs/language/en
/jobs/language/fr
```

### Language Pages

Bordful automatically generates language-specific pages listing all jobs requiring that language:

```
/jobs/language/en - All English jobs
/jobs/language/fr - All French jobs
```

A languages index page is also available at `/jobs/languages`.

### Job Alert Preferences

Users can subscribe to job alerts with language preferences. This allows them to receive notifications only for jobs matching their language skills.

## Language Detection

Bordful includes utilities for language detection and normalization:

### Language Name Lookup

```typescript
// Get language object by name
function getLanguageByName(name: string): LanguageDefinition | undefined

// Get language code from name
function getLanguageCode(name: string): string | undefined

// Get language name from code
function getLanguageName(code: string): string | undefined
```

### Language Normalization

Airtable data is automatically normalized to standard language codes:

```typescript
// Function to normalize language data from Airtable
function normalizeLanguages(value: unknown): LanguageCode[] {
  if (!value) return [];

  if (!Array.isArray(value)) {
    return [];
  }

  return value
    .map((item) => {
      if (typeof item === "string") {
        // Format 1: Extract code from "Language Name (code)" format
        const languageCodeMatch = /.*?\(([a-z]{2})\)$/i.exec(item);
        if (languageCodeMatch && languageCodeMatch[1]) {
          const extractedCode = languageCodeMatch[1].toLowerCase();

          // Verify the extracted code is valid
          if (LANGUAGE_CODES.includes(extractedCode as LanguageCode)) {
            return extractedCode as LanguageCode;
          }
        }

        // Format 2: Check if the string itself is a valid 2-letter code
        if (
          item.length === 2 &&
          LANGUAGE_CODES.includes(item.toLowerCase() as LanguageCode)
        ) {
          return item.toLowerCase() as LanguageCode;
        }

        // Format 3: Try to look up by language name
        const language = getLanguageByName(item);
        if (language) {
          return language.code as LanguageCode;
        }
      }

      return null;
    })
    .filter((code): code is LanguageCode => code !== null);
}
```

## Configuration Options

You can configure language-related settings in your `config/config.ts` file:

```typescript
// Default site language
language: "en",

// Job filter configuration
jobs: {
  filters: {
    // Enable/disable language filter
    enableLanguage: true,
  }
},

// Job alert configuration
email: {
  jobAlerts: {
    // Enable language preferences in job alerts
    enableLanguagePreferences: true,
  }
},
```

## Language UI Customization

### Language Display Names

You can customize how languages are displayed in the UI by modifying the `displayName` property in the language definition:

```typescript
// Example of customizing language display names
const CUSTOM_LANGUAGE_DISPLAY_NAMES: Partial<Record<LanguageCode, string>> = {
  en: "English (US)",
  "zh": "Chinese (Mandarin)",
};

// Apply custom display names
const languages = LANGUAGES.map(lang => ({
  ...lang,
  displayName: CUSTOM_LANGUAGE_DISPLAY_NAMES[lang.code] || lang.name
}));
```

### Language Icons

You can associate icons with languages by adding an icon mapping:

```typescript
// Example language icon mapping (using country flags)
const LANGUAGE_FLAGS: Partial<Record<LanguageCode, string>> = {
  en: "🇺🇸",
  fr: "🇫🇷",
  es: "🇪🇸",
  de: "🇩🇪",
  // Add more as needed
};

// Then use in your components
function LanguageTag({ code }: { code: LanguageCode }) {
  const flag = LANGUAGE_FLAGS[code] || "";
  const name = getLanguageName(code);
  
  return (
    <span className="language-tag">
      {flag} {name}
    </span>
  );
}
```

## Best Practices

### Data Entry in Airtable

For consistent language handling, follow these best practices in Airtable:

1. **Use the same format consistently** - Either ISO codes, "Language (code)" format, or language names
2. **Create a dropdown or single/multi-select field** - This prevents typos and ensures consistency
3. **Include language codes** - Including codes helps with precise identification

### SEO Benefits

Using proper language codes provides several SEO benefits:

1. **Improved search visibility** for specific language job searches
2. **Better matching** of jobs to candidates with specific language skills
3. **Enhanced structured data** for job listings

## Related Documentation

- [Language System](/docs/reference/language-system.md) - Complete documentation on Bordful's language system
- [Job Filtering System](/docs/guides/filtering-system.md) - Guide to setting up and customizing job filters
- [Job Alerts Configuration](/docs/guides/job-alerts.md) - Setting up job alerts with language preferences 
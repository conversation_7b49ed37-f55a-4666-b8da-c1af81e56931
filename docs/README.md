# Bordful Documentation

Welcome to the Bordful documentation. This directory contains comprehensive guides for setting up, configuring, and customizing your Bordful job board.

## Documentation Structure

- **[Getting Started](/docs/getting-started/)**: Quick setup guides and installation instructions
- **[Guides](/docs/guides/)**: Detailed guides for specific features and customizations
- **[Integrations](/docs/integrations/)**: Documentation for third-party service integrations
- **[Examples](/docs/examples/)**: Example configurations and usage scenarios
- **[Advanced](/docs/advanced/)**: Advanced topics like schema implementation and performance optimization
- **[Reference](/docs/reference/)**: Reference documentation for configuration options and APIs
- **[Troubleshooting](/docs/troubleshooting/)**: Solutions to common issues and frequently asked questions
- **[Contributing](/docs/contributing/)**: Guidelines for contributing to Bordful

## Documentation Standards

All documentation files follow a consistent format with:

1. **Frontmatter**: Each file includes title, description, and lastUpdated date
2. **Numbered Steps**: Clear, sequential steps for procedures
3. **Code Examples**: Syntax-highlighted code blocks with clear comments
4. **Next Steps**: Suggestions for related actions after completing the guide
5. **Related Guides**: Links to related documentation

## Creating New Documentation

To create new documentation:

1. Copy the `_template.md` file to the appropriate directory
2. Rename it to match your topic
3. Update the frontmatter with the correct title, description, and date
4. Fill in the content following the established structure
5. Add links to related documentation

## Documentation Formatting

- Use H2 (`##`) for main sections
- Use H3 (`###`) for subsections
- Use numbered lists for sequential steps
- Use bullet points for non-sequential items
- Include code blocks with appropriate language tags (bash, typescript, etc.)
- Include screenshots where helpful (place in `/public/docs/images/`)

## Contributing to Documentation

We welcome contributions to improve our documentation. Please see the [Documentation Contributing Guide](/docs/contributing/documentation-contribution.md) for details on our documentation standards and contribution process.

## Reference Documentation

- [Configuration Options](/docs/reference/configuration-options.md) - Complete reference for all configuration options
- [Environment Variables](/docs/reference/environment-variables.md) - Guide to required and optional environment variables
- [Enhanced Language System](/docs/reference/language-system.md) - Documentation for the internationalization-ready language system
- [Comprehensive FAQ System](/docs/reference/faq-system.md) - Documentation for the feature-rich FAQ page system
- [RSS Feed System](/docs/reference/rss-feed-system.md) - Documentation for the comprehensive RSS feed system
- [Robots.txt Generation](/docs/reference/robots-generation.md) - Documentation for automatic robots.txt generation
- [Sitemap Generation](/docs/reference/sitemap-generation.md) - Documentation for automatic XML sitemap generation
- [Salary Structure](/docs/reference/salary-structure.md) - Documentation for the comprehensive salary handling system
- [Pagination, Sorting, and URL Parameters](/docs/reference/pagination-sorting.md) - Documentation for the pagination system, sorting options, and URL parameters
- [Currencies](/docs/reference/currencies.md) - Guide to supported currencies and formatting options
- [Language Codes](/docs/reference/language-codes.md) - Reference for supported language codes
- [Career Levels](/docs/reference/career-levels.md) - Documentation for standardized career levels

## Advanced Topics

- [Script Management & Analytics](/docs/advanced/script-management.md) - Documentation for script loading and analytics integration
- [Data Revalidation](/docs/advanced/data-revalidation.md) - How Bordful keeps data fresh with ISR
- [Schema Implementation](/docs/advanced/schema-implementation.md) - Structured data for better SEO
- [Rate Limiting](/docs/advanced/rate-limiting.md) - Protect your API endpoints from abuse
- [Performance Optimization](/docs/advanced/performance-optimization.md) - Tips for optimizing your job board's performance

## Guides

- **[Navigation Customization](./guides/navigation.md)**: Configure and customize the navigation bar
- **[Footer Customization](./guides/footer.md)**: Configure and customize the footer
- **[Logo Customization](./guides/logo-customization.md)**: Configure and optimize your logo across the job board
- **[Hero Section Customization](./guides/hero-section.md)**: Configure backgrounds, colors, and styling for the hero section
- **[Core Features](./guides/core-features.md)**: Overview of Bordful's core features and capabilities
- **[Job Listings](./guides/job-listings.md)**: Configure and customize job listings display
- **[Filtering System](./guides/filtering-system.md)**: Configure the comprehensive job filtering system
- **[Pricing Page Customization](./guides/pricing.md)**: Configure and customize the pricing plans and options
- **[Contact Page Customization](./guides/contact.md)**: Configure and customize the contact page and support channels
- **[Email Provider Integration](./guides/email-integration.md)**: Configure email providers for job alerts and subscriptions
- **[Deployment](/docs/getting-started/deployment.md)**: Instructions for deploying your job board
- **[Job Alerts Configuration](/docs/guides/job-alerts.md)**: How to set up and customize job alerts
- **[Email Providers](/docs/guides/email-integration.md)**: Configuring email providers for notifications

## Customization

- **[Customization Guide](/docs/guides/customization.md)**: Comprehensive guide to styling, script management, and data source customization
- **[Theming and Color Customization](/docs/guides/theming-customization.md)**: Configure primary colors and theming
- **[Post Job Banner](/docs/guides/post-job-banner.md)**: Customize the "Post a Job" banner
- **[Currencies](/docs/reference/currencies.md)**: Configure and customize currency display

## Advanced Features

- **[Schema Implementation](/docs/advanced/schema-implementation.md)**: Structured data for better SEO
- **[Rate Limiting](/docs/advanced/rate-limiting.md)**: Protect your API endpoints from abuse

## Table of Contents

- [Email Providers](/docs/guides/email-integration.md) - Documentation for email provider integrations
- [Encharge Integration](/docs/integrations/encharge.md) - Detailed guide for setting up Encharge
- [Rate Limiting](/docs/advanced/rate-limiting.md) - Information about the rate limiting implementation
- [Deployment Guide](/docs/getting-started/deployment.md) - Instructions for deploying the job board to production
- [Currencies](/docs/reference/currencies.md) - Guide to using and configuring the 50+ supported currencies
- [Schema.org Implementation](/docs/advanced/schema-implementation.md) - Documentation for the comprehensive schema.org job posting implementation

## Overview

Bordful is an open-source job board platform built with Next.js, Tailwind CSS, and Airtable. It provides a modern, responsive interface for job seekers and a simple way for administrators to manage job listings.

## Getting Started

- [Quick Start Guide](/docs/getting-started/index.md) - Overview of how to get started with Bordful
- [Installation Guide](/docs/getting-started/installation.md) - Detailed instructions for installing and setting up Bordful
- [Airtable Setup Guide](/docs/getting-started/airtable-setup.md) - Comprehensive guide to setting up Airtable as the backend database
- [Configuration Guide](/docs/getting-started/configuration.md) - Comprehensive guide to configuring your Bordful job board

- [Deployment Guide](/docs/getting-started/deployment.md) - Instructions for deploying your job board to production

## Examples

- **[Feature Examples](/docs/examples/feature-examples.md)**: Real-world usage examples for key Bordful features
- **[Configuration Examples](/docs/examples/configuration-examples.md)**: Example configurations for different use cases
- **[Design Examples](/docs/examples/design-examples.md)**: Examples of different design approaches 
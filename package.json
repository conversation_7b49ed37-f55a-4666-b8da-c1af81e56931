{"name": "bordful", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fontsource/ibm-plex-serif": "^5.2.5", "@fontsource/inter": "^5.2.5", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "airtable": "^0.12.2", "axios": "^1.8.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "feed": "^5.1.0", "geist": "^1.3.1", "lucide-react": "^0.536.0", "next": "^15.4.5", "node-fetch": "^3.3.2", "nuqs": "^2.4.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-markdown": "^10.1.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "schema-dts": "^1.1.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "unified": "^11.0.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/airtable": "^0.10.0", "@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-config-next": "^15.4.5", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "^5.7.2"}}
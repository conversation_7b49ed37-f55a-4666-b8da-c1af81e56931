---
description: 
globs: 
alwaysApply: false
---
# Next.js App Router Structure

Bordful uses Next.js 15 with the App Router for file-based routing. Understanding this structure is crucial for navigation and adding new features.

## Core Layout

- [app/layout.tsx](mdc:app/layout.tsx) - Root layout with navigation, footer, and global providers
- [app/globals.css](mdc:app/globals.css) - Global styles and Tailwind CSS imports
- [app/page.tsx](mdc:app/page.tsx) - Homepage with job listings

## Page Routes

### Static Pages
- `app/about/` - About page
- `app/contact/` - Contact form page
- `app/faq/` - Frequently asked questions
- `app/pricing/` - Pricing plans page
- `app/terms/` - Terms of service
- `app/privacy/` - Privacy policy
- `app/changelog/` - Product changelog

### Job-Related Routes
- `app/jobs/` - Main jobs listing page
- `app/jobs/[category]/` - Generic category-based job filtering (currently unused)
- `app/jobs/[slug]/` - Individual job detail pages
- `app/jobs/language/[language]/` - Jobs filtered by programming language
- `app/jobs/languages/` - All programming languages listing
- `app/jobs/level/[level]/` - Jobs filtered by experience level
- `app/jobs/levels/` - All experience levels listing
- `app/jobs/location/[location]/` - Jobs filtered by location
- `app/jobs/locations/` - All locations listing
- `app/jobs/type/[type]/` - Jobs filtered by job type
- `app/jobs/types/` - All job types listing

### Special Features
- `app/job-alerts/` - Job alerts subscription page

## API Routes

### Core APIs
- `app/api/subscribe/` - Email subscription endpoint for job alerts
- `app/api/og/` - General Open Graph image generation
- `app/api/og/jobs/[slug]/` - Open Graph image generation for job posts

## Feed Generation

### RSS/Atom/JSON Feeds
- `app/feed.xml/` - RSS feed for job listings
- `app/atom.xml/` - Atom feed for job listings  
- `app/feed.json/` - JSON feed for job listings

## SEO & Meta Files

- [app/robots.ts](mdc:app/robots.ts) - Robots.txt generation
- [app/sitemap.ts](mdc:app/sitemap.ts) - Dynamic sitemap generation
- [app/not-found.tsx](mdc:app/not-found.tsx) - Custom 404 page
- `app/favicon.ico` - Site favicon

## Dynamic Route Patterns

### Job Filtering Routes
All job filtering routes follow the pattern:
- `/jobs/[category]/[value]/` - Filter jobs by specific category value
- `/jobs/[category]s/` - List all available values for a category

### Supported Categories
- `language` - Programming languages (e.g., `/jobs/language/javascript/`)
- `level` - Experience levels (e.g., `/jobs/level/senior/`)
- `location` - Job locations (e.g., `/jobs/location/remote/`)
- `type` - Job types (e.g., `/jobs/type/full-time/`)

## Route Generation Strategy

Routes are dynamically generated based on Airtable data:
1. **Static Generation**: Most pages are statically generated at build time
2. **ISR (Incremental Static Regeneration)**: Job pages use ISR for fresh content
3. **Dynamic Imports**: Components are lazy-loaded where appropriate

## Navigation Patterns

The navigation structure is defined in the configuration system and supports:
- Dropdown menus
- External links
- Icon integration
- Accessibility features
